<template>
  <div class="page-container">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="layui-card main-card">
        <div class="layui-card-header">子账号管理</div>
        <div class="layui-card-body">
          <div class="list_search">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="searchForm.account"
                  placeholder="搜索账号"
                  clearable
                />
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.status"
                  placeholder="账号状态"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="正常" value="normal" />
                  <el-option label="禁用" value="disabled" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.online"
                  placeholder="是否在线"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="在线" value="online" />
                  <el-option label="离线" value="offline" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleSearch" class="btn2">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="btn6">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格和分页容器 -->
    <div class="table-section">
      <div class="layui-card main-card table-card">
        <div class="layui-card-body">
          <!-- 表格容器 -->
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              style="width: 100%"
              stripe
              border
              :max-height="tableMaxHeight"
              class="stable-table"
            >
              <el-table-column prop="account" label="账号" min-width="120" />
              <el-table-column prop="level" label="会员等级" min-width="100" />
              <el-table-column prop="balance" label="余额" min-width="120" />
              <el-table-column prop="expireTime" label="会员到期" min-width="180" />
              <el-table-column prop="status" label="账号状态" min-width="100">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.status === '正常' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="loginTime" label="登陆时间" min-width="150" />
              <el-table-column prop="online" label="是否在线" min-width="100">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.online === '在线' ? 'success' : 'info'"
                    size="small"
                  >
                    {{ scope.row.online }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="storeCount" label="店铺数量" min-width="100" />
              <el-table-column prop="onlineStoreCount" label="店铺在线数量" min-width="120" />
              <!-- 操作列暂时隐藏 -->
              <!-- <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleEdit(scope.$index, scope.row)"
                    class="btn2"
                  >
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)"
                    class="btn4"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>

          <!-- 分页容器 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="prev, pager, next"
              prev-text="上一页"
              next-text="下一页"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              class="layui-pagination"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = ref({
  account: '',
  status: '',
  online: ''
})

// 表格数据
const tableData = ref([
  {
    account: '184762',
    level: 0,
    balance: '999.00',
    expireTime: '2025/7/5 21:04:34',
    status: '禁用',
    loginTime: '2025/7/5 21:04:34',
    online: '离线',
    storeCount: 1,
    onlineStoreCount: 0
  },
  {
    account: '184763',
    level: 0,
    balance: '999.00',
    expireTime: '2025/7/5 21:04:34',
    status: '正常',
    loginTime: '2025/7/5 21:04:34',
    online: '离线',
    storeCount: 1,
    onlineStoreCount: 0
  },
  {
    account: '184764',
    level: 1,
    balance: '1999.00',
    expireTime: '2025/8/5 21:04:34',
    status: '正常',
    loginTime: '2025/7/5 21:04:34',
    online: '在线',
    storeCount: 3,
    onlineStoreCount: 2
  },
  {
    account: '184765',
    level: 2,
    balance: '2999.00',
    expireTime: '2025/9/5 21:04:34',
    status: '正常',
    loginTime: '2025/7/5 21:04:34',
    online: '在线',
    storeCount: 5,
    onlineStoreCount: 4
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 计算表格最大高度
const tableMaxHeight = computed(() => {
  // 基础高度：视口高度减去头部、搜索区域、分页区域等固定高度
  const baseHeight = window.innerHeight - 300 // 预留300px给其他元素
  return Math.max(400, Math.min(600, baseHeight)) // 最小400px，最大600px
})
// 搜索
const handleSearch = () => {
  console.log('搜索:', searchForm.value)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理搜索按钮",
        text: JSON.stringify(searchForm.value)
      })
    })
  }

  ElMessage.success('搜索功能待实现')
}

// 重置
const handleReset = () => {
  searchForm.value = {
    account: '',
    status: '',
    online: ''
  }

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理重置按钮"
      })
    })
  }

  ElMessage.info('已重置搜索条件')
}

// 编辑
const handleEdit = (index, row) => {
  console.log('编辑:', index, row)
  ElMessage.info('编辑功能待实现')
}

// 删除
const handleDelete = (index, row) => {
  ElMessageBox.confirm(
    `确定要删除账号 ${row.account} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    tableData.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  console.log(`每页 ${val} 条`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理分页大小改变",
        pageSize: val
      })
    })
  }
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  console.log(`当前页: ${val}`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理分页按钮",
        page: val
      })
    })
  }
}

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置表格数据到全局变量
  window.table_zizhanghao = tableData.value
  window.table_zizhanghao_currentPage = currentPage.value
  window.table_zizhanghao_pageSize = pageSize.value
  window.table_zizhanghao_total = total.value
}

// 监听数据变化并更新全局变量
watch([tableData, currentPage, pageSize, total], () => {
  setupGlobalVariables()
}, { deep: true })

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.table_zizhanghao && Array.isArray(window.table_zizhanghao)) {
    tableData.value = window.table_zizhanghao
  }
  if (window.table_zizhanghao_currentPage) {
    currentPage.value = window.table_zizhanghao_currentPage
  }
  if (window.table_zizhanghao_pageSize) {
    pageSize.value = window.table_zizhanghao_pageSize
  }
  if (window.table_zizhanghao_total) {
    total.value = window.table_zizhanghao_total
  }
}

// 暴露更新函数到全局
window.updateSubAccountData = updateFromGlobalData

onMounted(() => {
  // 初始化数据
  setupGlobalVariables()
})
</script>

<style scoped>
/* 页面容器布局 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: calc(100vh - 120px); /* 减去头部导航高度 */
  padding: 20px;
  box-sizing: border-box;
  margin-top: -32px;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 16px;
}

/* 表格容器 */
.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.stable-table {
  height: 100%;
}

/* 分页容器 */
.pagination-wrapper {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  
}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.stable-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100% - 60px); /* 减去表头高度 */
}

:deep(.el-button.btn2) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.el-button.btn2:hover) {
  background-color: #6639e6;
  border-color: #6639e6;
}

:deep(.el-button.btn4) {
  background-color: #E67162;
  border-color: #E67162;
  color: #fff;
}

:deep(.el-button.btn4:hover) {
  background-color: #d85a4a;
  border-color: #d85a4a;
}

:deep(.el-button.btn6) {
  border: 1px solid #9494AA;
  color: #9494AA;
  
}

:deep(.el-button.btn6:hover) {
  background-color: #f5f5f5;
}

/* Layui 风格分页样式 */
:deep(.layui-pagination) {
  justify-content: flex-end;
}

:deep(.layui-pagination .btn-next),
:deep(.layui-pagination .btn-prev) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  padding: 0 15px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .btn-next:hover),
:deep(.layui-pagination .btn-prev:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .btn-next.is-disabled),
:deep(.layui-pagination .btn-prev.is-disabled) {
  color: #c0c4cc;
  background-color: #fff;
  border-color: #e2e2e2;
  cursor: not-allowed;
}

:deep(.layui-pagination .el-pager li) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  margin: 0 2px;
  min-width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .el-pager li:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .el-pager li.is-active) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.layui-pagination .el-pager li.is-active .layui-laypage-em) {
  position: absolute;
  left: -1px;
  top: -1px;
  padding: 1px;
  width: 100%;
  height: 100%;
  background-color: #7748F8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    min-height: calc(100vh - 80px);
  }

  .pagination-wrapper {
    padding: 12px 0;
  }

  :deep(.layui-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    overflow-x: auto;
  }

  :deep(.stable-table) {
    min-width: 800px;
  }
}
</style>
